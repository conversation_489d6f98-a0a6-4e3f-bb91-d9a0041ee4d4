
# Makefile - Unified project + Docker management for MSArkNet
# Usage: make <target>
# Default goal prints categorized help with emojis :)

# =========================
# 🔧 Variables
# =========================
DOCKER_COMPOSE := docker compose
PROJECT := msarknet
NETWORK := proxy
SERVICES := traefik mariadb adminer cerebro-fe cerebro-be docs

# Colors
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
NC := \033[0m

.DEFAULT_GOAL := help

##@ 🐳 Docker Compose Operations
.PHONY: install
install: ## 📦 Full install (first time only)
	@echo -e "$(BLUE)🚀 Installing environment...$(NC)"
	@chmod +x _scripts/setup.sh _scripts/hosts-functions.sh _scripts/generate-certs.sh
	@./_scripts/setup.sh
	@echo -e "$(GREEN)✅ Installation completed!$(NC)"

# Variables
COMPOSE_INFRA := docker compose -f docker-compose.infrastructure.yml
COMPOSE_CEREBRO := docker compose -f docker-compose.cerebro.yml
COMPOSE_HEALTH := docker compose -f docker-compose.health.yml
COMPOSE_DOCS := docker compose -f docker-compose.docs.yml

##@ 🎯 Grouped Services Management
.PHONY: up-infra up-cerebro up-health up-docs up-all
up-infra: network-create ## 🚀 Start infrastructure services
	@echo -e "$(GREEN)🚀 Starting infrastructure...$(NC)"
	@$(COMPOSE_INFRA) up -d
	@$(COMPOSE_INFRA) ps

up-cerebro: ## 🧠 Start Cerebro project
	@echo -e "$(GREEN)🧠 Starting Cerebro project...$(NC)"
	@$(COMPOSE_CEREBRO) up -d
	@$(COMPOSE_CEREBRO) ps

up-health: ## ❤️ Start Health project
	@echo -e "$(GREEN)❤️ Starting Health project...$(NC)"
	@$(COMPOSE_HEALTH) up -d
	@$(COMPOSE_HEALTH) ps

up-docs: ## 📚 Start Documentation
	@echo -e "$(GREEN)📚 Starting Documentation...$(NC)"
	@$(COMPOSE_DOCS) up -d
	@$(COMPOSE_DOCS) ps

up-all: up-infra up-cerebro up-health up-docs ## 🚀 Start all services

.PHONY: down-infra down-cerebro down-health down-docs down-all
down-infra: ## ⏹️ Stop infrastructure
	@$(COMPOSE_INFRA) down

down-cerebro: ## ⏹️ Stop Cerebro
	@$(COMPOSE_CEREBRO) down

down-health: ## ⏹️ Stop Health
	@$(COMPOSE_HEALTH) down

down-docs: ## ⏹️ Stop Documentation
	@$(COMPOSE_DOCS) down

down-all: down-docs down-health down-cerebro down-infra ## ⏹️ Stop all services

##@ 🔄 Development Workflows
.PHONY: dev-frontend dev-backend dev-full
dev-frontend: up-infra ## 🎨 Frontend development (infra + frontend apps)
	@$(COMPOSE_CEREBRO) up -d cerebro-fe
	@$(COMPOSE_HEALTH) up -d health-fe
	@echo -e "$(GREEN)✅ Frontend development environment ready$(NC)"

dev-backend: up-infra ## ⚙️ Backend development (infra + backend apps)
	@$(COMPOSE_CEREBRO) up -d cerebro-be
	@$(COMPOSE_HEALTH) up -d health-be
	@echo -e "$(GREEN)✅ Backend development environment ready$(NC)"

dev-full: up-infra dev-frontend dev-backend up-docs ## 🚀 Full development environment

##@ 🔍 Project-specific logs
logs-cerebro: ## 📝 Cerebro logs
	@$(COMPOSE_CEREBRO) logs -f

logs-health: ## 📝 Health logs
	@$(COMPOSE_HEALTH) logs -f

logs-infra: ## 📝 Infrastructure logs
	@$(COMPOSE_INFRA) logs -f

.PHONY: up
up: network-create ## 🚀 Up (detached) all services (safe if network exists)
	@echo -e "$(GREEN)🚀 Starting containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d
	@echo -e "$(GREEN)✅ Containers started$(NC)"
	@$(MAKE) status

.PHONY: up-build
up-build: network-create ## 🔨 Up with build
	@echo -e "$(YELLOW)🔨 Building and starting containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d --build
	@echo -e "$(GREEN)✅ Containers started$(NC)"
	@$(MAKE) status

.PHONY: down
down: ## ⏹️  Down (remove containers, keep volumes)
	@echo -e "$(RED)⏹️  Stopping containers...$(NC)"
	@$(DOCKER_COMPOSE) down
	@echo -e "$(GREEN)✅ Containers stopped$(NC)"

.PHONY: down-v
down-v: ## ⏹️  Down + remove volumes
	@echo -e "$(RED)⏹️  Stopping containers and removing volumes...$(NC)"
	@$(DOCKER_COMPOSE) down -v
	@echo -e "$(GREEN)✅ Containers stopped and volumes removed$(NC)"

.PHONY: restart
restart: ## 🔄 Restart all services
	@echo -e "$(BLUE)🔄 Restarting containers...$(NC)"
	@$(DOCKER_COMPOSE) restart

.PHONY: rebuild
rebuild: ## 🔨 Rebuild and restart all services
	@echo -e "$(YELLOW)🔨 Rebuilding and restarting containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d --build --force-recreate

.PHONY: pull
pull: ## ⬇️  Pull latest images
	@echo -e "$(CYAN)⬇️  Pulling latest images...$(NC)"
	@$(DOCKER_COMPOSE) pull

.PHONY: ps
ps: ## 📋 Show container status
	@$(DOCKER_COMPOSE) ps

.PHONY: logs
logs: ## 📝 Tail all logs
	@$(DOCKER_COMPOSE) logs -f

.PHONY: config
config: ## 🔧 Validate / view compose config
	@$(DOCKER_COMPOSE) config


##@ 📊 Statistics and Monitoring
.PHONY: stats
stats: ## 📊 Show container resource usage
	@echo -e "$(CYAN)📊 Container resource usage:$(NC)"
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

.PHONY: stats-live
stats-live: ## 📊 Show live container resource usage
	@echo -e "$(CYAN)📊 Live container resource usage:$(NC)"
	@docker stats
	.PHONY: health

health: ## 🏥 Check container health
	@echo -e "$(CYAN)🏥 Container Health Status:$(NC)"
	@for service in $(SERVICES); do \
		container_id=$$($(DOCKER_COMPOSE) ps -q $$service 2>/dev/null); \
		if [ ! -z "$$container_id" ]; then \
			health=$$(docker inspect --format='{{.State.Health.Status}}' $$container_id 2>/dev/null || echo "no-healthcheck"); \
			echo -e "  $(BLUE)$$service:$(NC) $$health"; \
		else \
			echo -e "  $(BLUE)$$service:$(NC) $(RED)not running$(NC)"; \
		fi; \
	done


##@ 🎯 Service Management
.PHONY: start-traefik
start-traefik: ## ▶️  Start only Traefik
	@$(DOCKER_COMPOSE) up -d traefik

.PHONY: stop-traefik
stop-traefik: ## ⏹️  Stop only Traefik
	@$(DOCKER_COMPOSE) stop traefik

.PHONY: restart-traefik
restart-traefik: ## 🔄 Restart only Traefik
	@echo -e "$(YELLOW)🔄 Restarting Traefik...$(NC)"
	@$(DOCKER_COMPOSE) restart traefik
	@echo -e "$(GREEN)✅ Traefik restarted$(NC)"


##@ 📝 Logs Management
.PHONY: logs-traefik
logs-traefik: ## 📝 Tail Traefik logs
	@$(DOCKER_COMPOSE) logs -f traefik

.PHONY: logs-cerebro-fe
logs-cerebro-fe: ## 📝 Tail Cerebro Frontend logs
	@$(DOCKER_COMPOSE) logs -f cerebro-fe

.PHONY: logs-cerebro-be
logs-cerebro-be: ## 📝 Tail Cerebro Backend logs
	@$(DOCKER_COMPOSE) logs -f cerebro-be

.PHONY: logs-grafana
logs-grafana: ## 📝 Tail Grafana logs
	@$(DOCKER_COMPOSE) logs -f grafana

.PHONY: logs-prometheus
logs-prometheus: ## 📝 Tail Prometheus logs
	@$(DOCKER_COMPOSE) logs -f prometheus

.PHONY: logs-portainer
logs-portainer: ## 📝 Tail Portainer logs
	@$(DOCKER_COMPOSE) logs -f portainer

.PHONY: logs-docs
logs-docs: ## 📝 Tail Documentation logs
	@$(DOCKER_COMPOSE) logs -f docs

.PHONY: logs-tail
logs-tail: ## 📝 Tail all logs
	@$(DOCKER_COMPOSE) logs --tail=100


##@ 🐚 Shell Access
.PHONY: shell-traefik
shell-traefik: ## 🐚 Access Traefik shell
	@$(DOCKER_COMPOSE) exec traefik sh || $(DOCKER_COMPOSE) exec traefik bash

.PHONY: shell-cerebro-fe
shell-cerebro-fe: ## 🐚 Access Cerebro Frontend shell
	@$(DOCKER_COMPOSE) exec cerebro-fe sh || $(DOCKER_COMPOSE) exec cerebro-fe bash

.PHONY: hell-cerebro-be
shell-cerebro-be: ## 🐚 Access Cerebro Backend shell
	@$(DOCKER_COMPOSE) exec cerebro-be sh || $(DOCKER_COMPOSE) exec cerebro-be bash


##@ 🔍 Inspection and Debugging
.PHONY: inspect-traefik
inspect-traefik: ## 🔍 Inspect Traefik container
	@docker inspect $(shell $(DOCKER_COMPOSE) ps -q traefik)

.PHONY: inspect-network
inspect-network: ## 🔍 Inspect proxy network
	@echo -e "$(CYAN)🔍 Inspecting $(NETWORK) network...$(NC)"
	@docker network inspect $(NETWORK)

.PHONY: inspect-all
inspect-all: ## 🔍 Inspect all containers
	@for service in $(SERVICES); do \
		echo -e "$(CYAN)🔍 Inspecting $$service...$(NC)"; \
		$(DOCKER_COMPOSE) ps $$service 2>/dev/null || echo "Service $$service not found"; \
	done

.PHONY: top
top: ## 🔍 Show running processes
	@$(DOCKER_COMPOSE) top

.PHONY: port-traefik port-all
port-traefik:	## 🔍 Show Traefik ports
	@$(DOCKER_COMPOSE) port traefik

.PHONY: port-all
port-all: ## 🔍 Show all ports
	@for service in $(SERVICES); do \
		echo -e "$(BLUE)Ports for $$service:$(NC)"; \
		$(DOCKER_COMPOSE) port $$service 2>/dev/null || echo "No exposed ports"; \
		echo ""; \
	done


##@ 🧹 Docker Cleanup
.PHONY: list-all
list-all: ## 📋 Lista todos los recursos Docker
	@echo "== Containers ==" && docker ps -a || true
	@echo "== Images ==" && docker images -a || true
	@echo "== Volumes ==" && docker volume ls || true
	@echo "== Networks ==" && docker network ls || true
	@echo "== Contexts ==" && docker context ls || true
	@echo "Current context:" && docker context show || true
	@echo "== Builders ==" && docker buildx ls || true

.PHONY: prune-safe
prune-safe: ## 🧹 Limpieza segura (solo no-usados)
	@docker system prune -a --volumes -f
	@docker builder prune -a -f || true
	@docker buildx prune -a -f || true

.PHONY: nuke-docker
nuke-docker: ## ☢️  BORRA TODO (contenedores, imágenes, volúmenes, redes, contexts y builds)
	@bash -c '$(MAKE) _nuke_script'

.PHONY: _nuke_script
_nuke_script:
	@bash -c '\
	CURRENT_CTX=$$(docker context show 2>/dev/null || echo default); \
	echo "Contexto actual: $$CURRENT_CTX"; \
	docker context use default 2>/dev/null || true; \
	IDS=$$(docker ps -aq); [ -n "$$IDS" ] && docker stop $$IDS || true; [ -n "$$IDS" ] && docker rm -f $$IDS || true; \
	IMG=$$(docker images -aq); [ -n "$$IMG" ] && docker rmi -f $$IMG || true; \
	VOL=$$(docker volume ls -q); [ -n "$$VOL" ] && docker volume rm $$VOL || true; \
	NET=$$(docker network ls -q | xargs -r docker network inspect --format "{{.Name}}" | grep -Ev "^(bridge|host|none)$$" || true); \
	[ -n "$$NET" ] && echo "$$NET" | xargs -r docker network rm || true; \
	docker builder prune -a -f || true; docker buildx prune -a -f || true; \
	INACTIVE_BUILDERS=$$(docker buildx ls 2>/dev/null | awk "/inactive/ {print \$$1}"); \
	[ -n "$$INACTIVE_BUILDERS" ] && echo "$$INACTIVE_BUILDERS" | xargs -r -n1 docker buildx rm || true; \
	for ctx in $$(docker context ls --format "{{.Name}}" 2>/dev/null); do \
	  if [ "$$ctx" != "default" ] && [ "$$ctx" != "$$CURRENT_CTX" ]; then docker context rm -f "$$ctx" || true; fi; \
	done; \
	docker system prune -a --volumes -f || true; \
	echo "✅ Docker limpiado a fondo."; \
	'

.PHONY: total-clean
total-clean: docker-clean docker-clean-all docker-clean-images docker-clean-containers docker-clean-volumes docker-clean-networks

.PHONY: docker-clean
docker-clean:	## 🧹 Clean Docker resources
	@echo -e "$(YELLOW)🧹 Cleaning Docker resources...$(NC)"
	@docker system prune -f
	@echo -e "$(GREEN)✅ Docker resources cleaned$(NC)"

.PHONY: docker-clean-all
docker-clean-all: ## 🧹 Clean all Docker resources (volumes included)
	@echo -e "$(RED)🧹 Cleaning all Docker resources...$(NC)"
	@docker system prune -a -f --volumes
	@echo -e "$(GREEN)✅ Docker resources cleaned$(NC)"

.PHONY: docker-clean-images
docker-clean-images:
	@echo -e "$(YELLOW)🧹 Cleaning Docker images...$(NC)"
	@docker image prune -a -f
	@echo -e "$(GREEN)✅ Docker images cleaned$(NC)"

.PHONY: docker-clean-containers
docker-clean-containers:
	@echo -e "$(YELLOW)🧹 Cleaning Docker containers...$(NC)"
	@docker container prune -f
	@echo -e "$(GREEN)✅ Docker containers cleaned$(NC)"

.PHONY: docker-clean-volumes
docker-clean-volumes:
	@echo -e "$(YELLOW)🧹 Cleaning Docker volumes...$(NC)"
	@docker volume prune -f
	@echo -e "$(GREEN)✅ Docker volumes cleaned$(NC)"

.PHONY: docker-clean-networks
docker-clean-networks:
	@echo -e "$(YELLOW)🧹 Cleaning Docker networks...$(NC)"
	@docker network prune -f
	@echo -e "$(GREEN)✅ Docker networks cleaned$(NC)"


##@ 🌐 Network Management
.PHONY: network-create
network-create: ## 🌐 Create network if not exists
	@docker network inspect $(NETWORK) >/dev/null 2>&1 || { \
		echo -e "$(BLUE)🌐 Creating $(NETWORK) network...$(NC)"; \
		docker network create $(NETWORK); \
	}
	@echo -e "$(YELLOW)⚠️  $(NETWORK) network ready$(NC)"

.PHONY: network-remove
network-remove: ## 🌐 Remove network
	@echo -e "$(RED)🌐 Removing $(NETWORK) network...$(NC)"
	@docker network rm $(NETWORK) 2>/dev/null || echo -e "$(YELLOW)⚠️  Network doesn't exist$(NC)"

.PHONY: network-ls
network-ls: ## 🌐 List networks
	@docker network ls

.PHONY: network-connect-traefik
network-connect-traefik: ## 🌐 Connect Traefik to network
	@docker network connect $(NETWORK) $(shell $(DOCKER_COMPOSE) ps -q traefik) 2>/dev/null || echo -e "$(YELLOW)⚠️  Already connected$(NC)"


##@ 🔐 Security Operations
.PHONY: check-permissions
check-permissions: ## 🔐 Check file permissions
	@echo -e "$(CYAN)🔐 Checking file permissions...$(NC)"
	@ls -la certs/ dynamic/ 2>/dev/null || echo "Some directories not found"

.PHONY: security-scan
security-scan: ## 🔐 Security scan
	@echo -e "$(CYAN)🔐 Security scan...$(NC)"
	@for service in $(SERVICES); do \
		container_id=$($(DOCKER_COMPOSE) ps -q $$service 2>/dev/null); \
		if [ ! -z "$$container_id" ]; then \
			echo -e "$(BLUE)Scanning $$service...$(NC)"; \
			docker exec $$container_id ps aux | grep -E "(root|www-data)" | wc -l | xargs echo "  Processes:"; \
			docker exec $$container_id netstat -tlnp 2>/dev/null | grep LISTEN | wc -l | xargs echo "  Open ports:"; \
		fi; \
	done

.PHONY: htpasswd
htpasswd: ## 🔐 Create htpasswd entry (bcrypt). Usage: make htpasswd USER=admin PASS=admin
	@if [ -z "$(user)" ] || [ -z "$(pass)" ]; then \
		echo "❌ Debes indicar user y pass, por ejemplo:"; \
		echo "   make htpasswd user=admin pass=admin"; \
		exit 1; \
	fi
	@htpasswd -nbB $(user) "$(pass)"

.PHONY: certs
certs: ## 🔒 Generate SSL certificates
	@echo -e "$(YELLOW)🔒 Generating SSL certificates...$(NC)"
	@./_scripts/generate-certs.sh
	@echo -e "$(GREEN)✅ Certificates generated$(NC)"

.PHONY: certs-info
certs-info: ## 📋 View certificate information
	@echo -e "$(CYAN)📋 Certificate information:$(NC)"
	@if [ -f certs/$(CERT_DOMAIN).crt ]; then \
		openssl x509 -in certs/$(CERT_DOMAIN).crt -text -noout | grep -A1 "Subject:\|Not Before\|Not After\|DNS:"; \
	else \
		echo -e "$(RED)❌ Certificado no encontrado. Ejecuta 'make certs'$(NC)"; \
	fi

.PHONY: certs-clean
certs-clean: ## 🧹 Clean generated certificates
	@echo -e "$(YELLOW)🧹 Cleaning certificates...$(NC)"
	@rm -rf certs/*.crt certs/*.key certs/*.conf
	@echo -e "$(GREEN)✅ Certificates cleaned$(NC)"


##@ 🌐 Hosts File Management
# Usage examples:
#   make hosts-check HOSTS="msarknet.me grafana.msarknet.me"
#   make hosts-add   HOSTS="msarknet.me,api.msarknet.me" IP=127.0.0.1 COMMENT="MsArkNet local"
#   make hosts-remove HOSTS="msarknet.me api.msarknet.me"
#   make hosts-add-line LINE="127.0.0.1 myapp.local # custom"

HOSTS_FILE ?= /etc/hosts
IP ?= 127.0.0.1
COMMENT ?= Managed by Makefile

.PHONY: hosts-add hosts-remove hosts-add-line

hosts-add: ## ➕ Add entries for HOSTS at IP (idempotent)
	@set -e; \
	if [ -z "$(HOSTS)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide HOSTS (e.g., HOSTS=\"msarknet.me api.msarknet.me\")$(NC)"; \
		exit 1; \
	fi; \
	for h in $$(echo "$(HOSTS)" | tr ',' ' '); do \
		if grep -E "^[^#]*\\b$$h\\b" "$(HOSTS_FILE)" >/dev/null; then \
			echo -e "$(YELLOW)↪️  Skipping (exists):$(NC) $$h"; \
		else \
			echo -e "$(BLUE)➕ Adding:$(NC) $(IP) $$h  # $(COMMENT)"; \
			echo "$(IP) $$h	# $(COMMENT)" | sudo tee -a "$(HOSTS_FILE)" >/dev/null; \
		fi; \
	done

hosts-add-line: ## ➕ Add a raw LINE to hosts (use LINE="127.0.0.1 foo # comment")
	@set -e; \
	if [ -z "$(LINE)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide LINE (e.g., LINE=\"127.0.0.1 foo.local # comment\")$(NC)"; \
		exit 1; \
	fi; \
	echo -e "$(BLUE)➕ Adding raw line:$(NC) $(LINE)"; \
	echo "$(LINE)" | sudo tee -a "$(HOSTS_FILE)" >/dev/null

hosts-remove:  ## ➖ Remove any lines that contain each host in HOSTS
	@set -e; \
	if [ -z "$(HOSTS)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide HOSTS (e.g., HOSTS=\"msarknet.me api.msarknet.me\")$(NC)"; \
		exit 1; \
	fi; \
	for h in $$(echo "$(HOSTS)" | tr ',' ' '); do \
		echo -e "$(RED)➖ Removing entries containing:$(NC) $$h"; \
		sudo sed -i.bak '/\\b'$$h'\\b/d' "$(HOSTS_FILE)"; \
	done; \
	echo -e "$(GREEN)✅ Hosts entries processed$(NC)"


##@ 📈 Performance Operations
.PHONY: resource-limits
resource-limits:
	@echo -e "$(CYAN)📊 Container resource limits:$(NC)"
	@for service in $(SERVICES); do \
		container_id=$($(DOCKER_COMPOSE) ps -q $$service 2>/dev/null); \
		if [ ! -z "$$container_id" ]; then \
			echo -e "$(BLUE)$$service:$(NC)"; \
			docker inspect $$container_id | jq -r '.[0].HostConfig | "  CPU: \(.CpuShares // \"unlimited\"), Memory: \(.Memory // \"unlimited\")"' 2>/dev/null || echo "  No limits set"; \
		fi; \
	done


##@ ⚡ Quick Actions
.PHONY: quick-status
quick-status status: ## ⚡ Quick status check
	@echo -e "$(CYAN)⚡ Quick Status:$(NC)"
	@$(DOCKER_COMPOSE) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"


##@ 📚 Information
.PHONY: docker-info
docker-info: ## ℹ️  Show Docker system information
	@echo -e "$(CYAN)ℹ️  Docker System Information:$(NC)"
	@echo -e "$(BLUE)Docker version:$(NC)"; docker --version
	@echo -e "$(BLUE)Docker Compose version:$(NC)"; docker compose version
	@echo -e "$(BLUE)Available networks:$(NC)"; docker network ls | grep -E "($(NETWORK)|bridge)"
	@echo -e "$(BLUE)Project containers:$(NC)"; $(DOCKER_COMPOSE) ps

.PHONY: container-sizes
container-sizes: ## 📊 Show container sizes
	@echo -e "$(CYAN)📊 Container sizes:$(NC)"
	@docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(traefik|nginx|whoami)" || true

.PHONY: help
help: ## 💡 Show this help
	@awk 'BEGIN {FS = ":.*##"; printf "\n$(CYAN)🐳 MSArkNet Docker Management$(NC)\n\n"} /^[a-zA-Z0-9_.-]+:.*?##/ { printf "  $(GREEN)%-25s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(YELLOW)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
